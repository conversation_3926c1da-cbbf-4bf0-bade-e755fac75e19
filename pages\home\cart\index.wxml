<!-- 自定义导航栏 -->
<custom-navbar title="耀楼惠企通" show-back="{{false}}" />

<view style="display: flex;flex-direction: column;height: calc(100vh - 88px - var(--tabbarHeight) - env(safe-area-inset-bottom));width: 100%;overflow: hidden;">

  <view class="cart-head">
    <text style="flex: 1;font-size: 28rpx;line-height: 100rpx;color: #333;">全部({{cartList.length}})</text>
    <view wx:if="{{cartList.length>0}}" style="display: flex;justify-self: start;align-items: center;width: 100rpx;height: 100%;" bind:tap="removeAll">
      <t-icon name="delete" color="#999" size="34rpx" />
      <text style="font-size: 28rpx;margin-left: 8rpx;color: #333;">清空</text>
    </view>
  </view>

  <block wx:if="{{cartList.length > 0}}">
    <scroll-view scroll-y="true" class="cart-list">
      <cart-goods wx:for="{{cartList}}" wx:key="id" cart-info="{{ item }}" bindhandleSelectGoods="handleSelectGoods" bindhandleDeleteGoods="handleDeleteGoods" bindhandleChangeNum="handleChangeNum" />
    </scroll-view>

    <!-- 商品小计以及结算按钮 -->
    <cart-bar is-all-selected="{{cartGroupData.isAllSelected}}" total-amount="{{cartGroupData.totalAmount}}" total-goods-num="{{cartGroupData.selectedGoodsCount}}" total-discount-amount="{{cartGroupData.totalDiscountAmount}}" bindhandleSelectAll="handleSelectAll" bindhandleToSettle="onToSettle" />
  </block>

  <!-- 购物车空态 -->
  <cart-empty wx:else bind:handleClick="onGotoHome" />

</view>

<!-- 悬浮客服按钮 -->
<floating-kefu init-x="600" init-y="700" />