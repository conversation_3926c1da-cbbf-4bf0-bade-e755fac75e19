/* pages/cart/index.wxss */
page {
  background: var(--pageBgColor);
  height: 100vh;
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}



.cart-head {
  position: relative;
  display: flex;
  justify-self: start;
  align-items: center;
  background-color: #fff;
  height: 100rpx;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
  z-index: 10;
}

.cart-list {
  height: calc(100vh - var(--tabbarHeight) - env(safe-area-inset-bottom) - 100rpx - 112rpx);
}