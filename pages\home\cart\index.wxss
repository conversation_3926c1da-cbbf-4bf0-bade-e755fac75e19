/* pages/cart/index.wxss */
page {
  background: var(--pageBgColor);
  height: 100vh;
}

/* 购物车容器 */
.cart-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  padding-top: 88px; /* 自定义导航栏高度 */
  box-sizing: border-box;
}

/* 底部安全区域 */
.safe-area-bottom {
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
}

.cart-head {
  position: relative;
  display: flex;
  justify-self: start;
  align-items: center;
  background-color: #fff;
  height: 100rpx;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
  z-index: 10;
  flex-shrink: 0;
}

.cart-list {
  flex: 1;
  overflow-y: auto;
}

/* 底部结算栏容器 */
.cart-bar-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding-bottom: calc(var(--tabbarHeight) + env(safe-area-inset-bottom));
  background-color: #fff;
}